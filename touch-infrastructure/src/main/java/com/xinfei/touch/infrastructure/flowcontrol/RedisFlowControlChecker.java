package com.xinfei.touch.infrastructure.flowcontrol;

import com.xinfei.touch.domain.model.FlowControlRule;
import com.xinfei.touch.domain.service.FlowControlChecker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的频控检查器实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisFlowControlChecker implements FlowControlChecker {
    
    private final RedissonClient redissonClient;
    
    @Override
    public boolean check(String key, Integer limitTimes, Integer limitSeconds) {
        try {
            log.debug("执行频控检查: key={}, limitTimes={}, limitSeconds={}", key, limitTimes, limitSeconds);
            
            // 使用Redis原子操作实现滑动窗口频控
            String countKey = "flow_control:count:" + key;
            RAtomicLong counter = redissonClient.getAtomicLong(countKey);
            
            // 获取当前计数
            long currentCount = counter.get();
            
            // 如果是第一次访问，设置过期时间
            if (currentCount == 0) {
                counter.set(1);
                counter.expire(limitSeconds, TimeUnit.SECONDS);
                return true;
            }
            
            // 检查是否超过限制
            if (currentCount >= limitTimes) {
                log.info("频控拦截: key={}, currentCount={}, limitTimes={}", key, currentCount, limitTimes);
                return false;
            }
            
            // 增加计数
            counter.incrementAndGet();
            
            return true;
            
        } catch (Exception e) {
            log.error("频控检查异常: key={}", key, e);
            // 异常情况下放行，避免影响业务
            return true;
        }
    }
    
    @Override
    public boolean acquireLock(String lockKey, int expireSeconds) {
        try {
            log.debug("获取分布式锁: lockKey={}, expireSeconds={}", lockKey, expireSeconds);
            
            String redisLockKey = "flow_control:lock:" + lockKey;
            RLock lock = redissonClient.getLock(redisLockKey);
            
            // 尝试获取锁，等待时间为0，锁过期时间为expireSeconds
            boolean acquired = lock.tryLock(0, expireSeconds, TimeUnit.SECONDS);
            
            if (acquired) {
                log.debug("成功获取分布式锁: lockKey={}", lockKey);
            } else {
                log.debug("获取分布式锁失败: lockKey={}", lockKey);
            }
            
            return acquired;
            
        } catch (Exception e) {
            log.error("获取分布式锁异常: lockKey={}", lockKey, e);
            return false;
        }
    }
    
    @Override
    public void releaseLock(String lockKey) {
        try {
            log.debug("释放分布式锁: lockKey={}", lockKey);
            
            String redisLockKey = "flow_control:lock:" + lockKey;
            RLock lock = redissonClient.getLock(redisLockKey);
            
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("成功释放分布式锁: lockKey={}", lockKey);
            } else {
                log.warn("锁不是由当前线程持有，无法释放: lockKey={}", lockKey);
            }
            
        } catch (Exception e) {
            log.error("释放分布式锁异常: lockKey={}", lockKey, e);
        }
    }
    
    @Override
    public List<Long> batchFilter(List<Long> userIds, FlowControlRule rule) {
        log.debug("批量频控过滤: userCount={}, rule={}", userIds.size(), rule.getRuleName());
        
        List<Long> passedUserIds = new ArrayList<>();
        
        for (Long userId : userIds) {
            String key = rule.buildFlowControlKey(userId);
            if (check(key, rule.getLimitTimes(), rule.getLimitSeconds())) {
                passedUserIds.add(userId);
            }
        }
        
        log.debug("批量频控过滤完成: 原始数量={}, 通过数量={}", userIds.size(), passedUserIds.size());
        
        return passedUserIds;
    }
    
    @Override
    public Long getCurrentCount(String key) {
        try {
            String countKey = "flow_control:count:" + key;
            RAtomicLong counter = redissonClient.getAtomicLong(countKey);
            return counter.get();
        } catch (Exception e) {
            log.error("获取当前计数异常: key={}", key, e);
            return 0L;
        }
    }
    
    @Override
    public void resetCount(String key) {
        try {
            String countKey = "flow_control:count:" + key;
            RAtomicLong counter = redissonClient.getAtomicLong(countKey);
            counter.delete();
            log.info("重置频控计数: key={}", key);
        } catch (Exception e) {
            log.error("重置频控计数异常: key={}", key, e);
        }
    }
}
