package com.xinfei.touch;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 触达服务启动类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication
@EnableFeignClients
@EnableAsync
@EnableScheduling
public class TouchServiceApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(TouchServiceApplication.class, args);
    }
}
