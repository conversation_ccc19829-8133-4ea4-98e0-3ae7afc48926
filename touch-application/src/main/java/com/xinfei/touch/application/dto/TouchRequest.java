package com.xinfei.touch.application.dto;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import lombok.Data;

import java.util.Map;

/**
 * 触达请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchRequest {
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 触达类型
     */
    private TouchType touchType;
    
    /**
     * 触达渠道
     */
    private TouchChannel channel;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务事件类型
     */
    private String bizEventType;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 触达配置
     */
    private TouchConfigDto touchConfig;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extParams;

    // ========== 原DispatchDto中的重要字段 ==========

    /**
     * 策略执行ID
     */
    private String strategyExecId;

    /**
     * 明细表序号
     */
    private String detailTableNo;

    /**
     * 策略分组ID
     */
    private Long strategyGroupId;

    /**
     * 策略分组名称
     */
    private String strategyGroupName;

    /**
     * 策略渠道ID
     */
    private Long strategyChannelId;

    /**
     * 渠道模板ID
     */
    private String templateId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 触达类型（NOTIFY为通知，不流控）
     */
    private String dispatchType;

    /**
     * 短信签名
     */
    private String signatureKey;

    /**
     * 优惠券活动ID
     */
    private String activityId;

    /**
     * 类型ID
     */
    private String nameTypeId;

    /**
     * 业务线类型
     */
    private String bizType;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 应用标识
     */
    private String app;

    /**
     * 内部应用标识
     */
    private String innerApp;

    /**
     * 设备ID（Push渠道使用）
     */
    private String deviceId;

    /**
     * 手机号（短信、电销渠道使用）
     */
    private String mobile;

    /**
     * 追踪ID
     */
    private String traceId;
}
