package com.xinfei.touch.application.command;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import lombok.Data;

import java.util.Map;

/**
 * 触达命令对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchCommand {
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 触达类型
     */
    private TouchType touchType;
    
    /**
     * 触达渠道
     */
    private TouchChannel channel;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务事件类型
     */
    private String bizEventType;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 触达配置
     */
    private TouchConfigCommand touchConfig;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> extParams;
    
    /**
     * 创建触达命令
     */
    public static TouchCommand create(String requestId, TouchType touchType, TouchChannel channel, 
                                     Long strategyId, Long userId) {
        TouchCommand command = new TouchCommand();
        command.setRequestId(requestId);
        command.setTouchType(touchType);
        command.setChannel(channel);
        command.setStrategyId(strategyId);
        command.setUserId(userId);
        command.setTimestamp(System.currentTimeMillis());
        return command;
    }
}
